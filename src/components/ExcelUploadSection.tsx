import React from 'react';
import { Box, Card, CardContent } from '@mui/material';
import type { UploadedFile } from './types';
import { useFileUpload } from '../context/FileUploadContext';
import {
  ExcelSectionHeader,
  ExcelUploadArea,
  ExcelFileList,
  ExcelFileSelector,
  useExcelUpload,
} from './excel';

interface ExcelUploadSectionProps {
  files: UploadedFile[];
  onFileUpload: (files: UploadedFile[]) => void;
  onFileDelete: (fileId: string) => void;
}

const ExcelUploadSection: React.FC<ExcelUploadSectionProps> = ({
  files,
  onFileUpload,
  onFileDelete,
}) => {
  const { handleFileUpload } = useExcelUpload({ onFileUpload });
  const { selectedExcelFile, setSelectedExcelFile } = useFileUpload();

  return (
    <Box sx={{ flex: 1 }}>
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <ExcelSectionHeader />
          <ExcelUploadArea onFileUpload={handleFileUpload} />

          {/* File selector for language codes */}
          {files.length > 0 && (
            <ExcelFileSelector
              files={files}
              selectedFile={selectedExcelFile}
              onFileSelect={setSelectedExcelFile}
            />
          )}

          <ExcelFileList
            files={files}
            onFileDelete={onFileDelete}
            selectedFileId={selectedExcelFile?.id}
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default ExcelUploadSection;
