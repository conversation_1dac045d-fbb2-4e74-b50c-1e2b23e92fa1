import React, { useState } from 'react';
import { IconButton, Tooltip } from '@mui/material';
import { Visibility as PreviewIcon } from '@mui/icons-material';
import PdfPreviewDialog from './PdfPreviewDialog';

interface PdfPreviewActionProps {
  file: File | null;
  fileName: string;
  disabled?: boolean;
}

const PdfPreviewAction: React.FC<PdfPreviewActionProps> = ({
  file,
  fileName,
  disabled = false,
}) => {
  const [previewOpen, setPreviewOpen] = useState(false);

  const handlePreviewClick = () => {
    if (file) {
      setPreviewOpen(true);
    }
  };

  const handlePreviewClose = () => {
    setPreviewOpen(false);
  };

  return (
    <>
      <Tooltip title={disabled ? "PDF file not available for preview" : "Preview PDF"}>
        <span>
          <IconButton
            onClick={handlePreviewClick}
            disabled={disabled || !file}
            size="small"
            sx={{ mr: 1 }}
          >
            <PreviewIcon />
          </IconButton>
        </span>
      </Tooltip>

      <PdfPreviewDialog
        open={previewOpen}
        onClose={handlePreviewClose}
        file={file}
        fileName={fileName}
      />
    </>
  );
};

export default PdfPreviewAction;
