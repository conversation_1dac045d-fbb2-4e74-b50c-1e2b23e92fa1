import React from 'react';
import { Typography, List } from '@mui/material';
import type { UploadedFile } from '../types';
import PdfFileItem from './PdfFileItem';

interface PdfFileListProps {
  files: UploadedFile[];
  onFileDelete: (fileId: string) => void;
  onLanguageChange: (fileId: string, language: string) => void;
  availableLanguages: string[];
}

const PdfFileList: React.FC<PdfFileListProps> = ({ 
  files, 
  onFileDelete, 
  onLanguageChange,
  availableLanguages 
}) => {
  return (
    <>
      <Typography variant="h6" gutterBottom>
        Uploaded PDF Files ({files.length})
      </Typography>
      
      {files.length === 0 ? (
        <Typography variant="body2" color="text.secondary">
          No PDF files uploaded yet.
        </Typography>
      ) : (
        <List dense>
          {files.map((file) => (
            <PdfFileItem
              key={file.id}
              file={file}
              onDelete={onFileDelete}
              onLanguageChange={onLanguageChange}
              availableLanguages={availableLanguages}
            />
          ))}
        </List>
      )}
    </>
  );
};

export default PdfFileList;
