import React from 'react';
import { Typography, Button } from '@mui/material';
import { CloudUpload as CloudUploadIcon, PictureAsPdf as PdfIcon } from '@mui/icons-material';
import { UploadArea, VisuallyHiddenInput } from '../shared/UploadStyles';

interface PdfUploadAreaProps {
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const PdfUploadArea: React.FC<PdfUploadAreaProps> = ({ onFileUpload }) => {
  return (
    <UploadArea sx={{ mb: 3 }}>
      <PdfIcon sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
      <Typography variant="h6" gutterBottom>
        Drop PDF Files Here
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        or click to browse
      </Typography>
      <Button
        component="label"
        variant="contained"
        color="error"
        startIcon={<CloudUploadIcon />}
      >
        Choose PDF Files
        <VisuallyHiddenInput
          type="file"
          multiple
          accept=".pdf"
          onChange={onFileUpload}
        />
      </Button>
    </UploadArea>
  );
};

export default PdfUploadArea;
