import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
} from '@mui/material';
import type { SelectChangeEvent } from '@mui/material/Select';

interface PdfLanguageSelectorProps {
  fileId: string;
  selectedLanguage?: string;
  availableLanguages: string[];
  onLanguageChange: (fileId: string, language: string) => void;
}

const PdfLanguageSelector: React.FC<PdfLanguageSelectorProps> = ({
  fileId,
  selectedLanguage,
  availableLanguages,
  onLanguageChange,
}) => {
  const handleChange = (event: SelectChangeEvent<string>) => {
    onLanguageChange(fileId, event.target.value);
  };

  if (availableLanguages.length === 0) {
    return (
      <Box sx={{ px: 2, pb: 2 }}>
        <Typography variant="caption" color="text.secondary">
          No language codes available. Upload Excel files first.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ pr: 1}}>
      <FormControl fullWidth size="small">
        <InputLabel id={`language-select-${fileId}`}>
          Select Language
        </InputLabel>
        <Select
          labelId={`language-select-${fileId}`}
          id={`language-select-${fileId}`}
          value={selectedLanguage || ''}
          label={`Select Language`}
          onChange={handleChange}
        >
          <MenuItem value="">
            <em>No language selected</em>
          </MenuItem>
          {availableLanguages.map((language) => (
            <MenuItem key={language} value={language}>
              {language}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default PdfLanguageSelector;
