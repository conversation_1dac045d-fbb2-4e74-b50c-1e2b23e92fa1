import React from 'react';
import { Box, ListItem, ListItemText, IconButton, Chip } from '@mui/material';
import { Delete as DeleteIcon, PictureAsPdf as PdfIcon, Language as LanguageIcon } from '@mui/icons-material';
import type { UploadedFile } from '../types';
import { formatFileSize } from '../types';
import PdfLanguageSelector from './PdfLanguageSelector';
import PdfPreviewAction from './PdfPreviewAction';

interface PdfFileItemProps {
  file: UploadedFile;
  onDelete: (fileId: string) => void;
  onLanguageChange: (fileId: string, language: string) => void;
  availableLanguages: string[];
}

const PdfFileItem: React.FC<PdfFileItemProps> = ({ 
  file, 
  onDelete, 
  onLanguageChange,
  availableLanguages 
}) => {
  return (
    <Box>
      <ListItem
        divider
       sx={{gap:1}}
      >
         <PdfLanguageSelector
        fileId={file.id}
        fileName={file.name}
        selectedLanguage={file.selectedLanguage}
        availableLanguages={availableLanguages}
        onLanguageChange={onLanguageChange}
      />
        <PdfIcon sx={{ mr: 2, color: 'error.main' }} />
        <ListItemText
          primary={file.name}
          secondary={formatFileSize(file.size)}
        />
        { actions(file, onDelete)}
      </ListItem>

      {/* Language selector for each PDF file */}
     
    </Box>
  );
};

export default PdfFileItem;


function actions(file: UploadedFile, onDelete: (fileId: string) => void): React.ReactNode {
  return <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
    {file.selectedLanguage && (
      <Chip
        icon={<LanguageIcon />}
        label={file.selectedLanguage}
        size="small"
        color="error"
        variant="filled" />
    )}
    <PdfPreviewAction
      file={file.originalFile || null}
      fileName={file.name}
      disabled={!file.originalFile}
    />
    <IconButton
      edge="end"
      aria-label="delete"
      onClick={() => onDelete(file.id)}
    >
      <DeleteIcon />
    </IconButton>
  </Box>;
}

