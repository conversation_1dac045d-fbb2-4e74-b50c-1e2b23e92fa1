import React from 'react';
import { Typography, Box } from '@mui/material';
import { PictureAsPdf as PdfIcon } from '@mui/icons-material';

const PdfSectionHeader: React.FC = () => {
  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <PdfIcon sx={{ mr: 1, color: 'error.main' }} />
        <Typography variant="h5" component="h2">
          PDF Documents
        </Typography>
      </Box>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Upload PDF files for document analysis and narrative comparison.
      </Typography>
    </>
  );
};

export default PdfSectionHeader;
