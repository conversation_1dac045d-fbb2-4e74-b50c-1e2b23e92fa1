import React from 'react';
import { Box, Card, CardContent } from '@mui/material';
import type { UploadedFile } from './types';
import { useFileUpload } from '../context/FileUploadContext';
import {
  PdfSectionHeader,
  PdfUploadArea,
  PdfFileList,
} from './pdf';

interface PdfUploadSectionProps {
  files: UploadedFile[];
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFileDelete: (fileId: string) => void;
}

const PdfUploadSection: React.FC<PdfUploadSectionProps> = ({
  files,
  onFileUpload,
  onFileDelete,
}) => {
  const { availableLanguageCodes, updatePdfLanguage } = useFileUpload();

  const handleLanguageChange = (fileId: string, language: string) => {
    updatePdfLanguage(fileId, language);
  };

  return (
    <Box sx={{ flex: 1 }}>
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <PdfSectionHeader />
          <PdfUploadArea onFileUpload={onFileUpload} />
          <PdfFileList
            files={files}
            onFileDelete={onFileDelete}
            onLanguageChange={handleLanguageChange}
            availableLanguages={availableLanguageCodes}
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default PdfUploadSection;
