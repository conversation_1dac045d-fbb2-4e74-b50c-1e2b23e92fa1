export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  category: 'excel' | 'pdf';
  columnHeaders?: string[]; // For Excel files
  selectedLanguage?: string; // For PDF files - selected language code
  originalFile?: File; // Store original File object for preview
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
