import React from 'react';
import { Typography, Button } from '@mui/material';
import { CloudUpload as CloudUploadIcon, TableChart as ExcelIcon } from '@mui/icons-material';
import { UploadArea, VisuallyHiddenInput } from '../shared/UploadStyles';

interface ExcelUploadAreaProps {
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const ExcelUploadArea: React.FC<ExcelUploadAreaProps> = ({ onFileUpload }) => {
  return (
    <UploadArea sx={{ mb: 3 }}>
      <ExcelIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
      <Typography variant="h6" gutterBottom>
        Drop Excel Files Here
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        or click to browse
      </Typography>
      <Button
        component="label"
        variant="contained"
        color="success"
        startIcon={<CloudUploadIcon />}
      >
        Choose Excel Files
        <VisuallyHiddenInput
          type="file"
          multiple
          accept=".xlsx,.xls"
          onChange={onFileUpload}
        />
      </Button>
    </UploadArea>
  );
};

export default ExcelUploadArea;
