import React from 'react';
import { Box, ListItem, ListItemText, IconButton, Chip } from '@mui/material';
import { Delete as DeleteIcon, TableChart as ExcelIcon, CheckCircle as SelectedIcon } from '@mui/icons-material';
import type { UploadedFile } from '../types';
import { formatFileSize } from '../types';
import LanguageCodeChips from './LanguageCodeChips';

interface ExcelFileItemProps {
  file: UploadedFile;
  onDelete: (fileId: string) => void;
  isSelected?: boolean;
}

const ExcelFileItem: React.FC<ExcelFileItemProps> = ({ file, onDelete, isSelected = false }) => {
  return (
    <Box sx={{
      border: isSelected ? '2px solid' : 'none',
      borderColor: isSelected ? 'primary.main' : 'transparent',
      borderRadius: 1,
      backgroundColor: isSelected ? 'action.selected' : 'transparent',
    }}>
      <ListItem
        divider
        secondaryAction={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {isSelected && (
              <Chip
                icon={<SelectedIcon />}
                label="Selected"
                size="small"
                color="primary"
                variant="filled"
              />
            )}
            <IconButton
              edge="end"
              aria-label="delete"
              onClick={() => onDelete(file.id)}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        }
      >
        <ExcelIcon sx={{ mr: 2, color: 'success.main' }} />
        <ListItemText
          primary={file.name}
          secondary={formatFileSize(file.size)}
        />
      </ListItem>

      {/* Display language code headers as chips */}
      {file.columnHeaders && file.columnHeaders.length > 0 && (
        <LanguageCodeChips columnHeaders={file.columnHeaders} />
      )}
    </Box>
  );
};

export default ExcelFileItem;
