import * as XLSX from 'xlsx';

/**
 * Reads the header row from an Excel file and returns column headers
 */
export const readExcelHeaders = async (file: File): Promise<string[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Get the range of the worksheet
        const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
        const headers: string[] = [];

        // Read the first row (headers)
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
          const cell = worksheet[cellAddress];
          headers.push(cell ? String(cell.v) : `Column ${col + 1}`);
        }

        resolve(headers);
      } catch (error) {
        console.error('Error reading Excel file:', error);
        reject(error);
      }
    };
    
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsArrayBuffer(file);
  });
};

/**
 * Filters column headers to return only language codes (headers containing underscore)
 */
export const extractLanguageCodes = (headers: string[]): string[] => {
  return headers.filter(header => header.includes('_'));
};

/**
 * Generates a unique ID for uploaded files
 */
export const generateFileId = (): string => {
  return Math.random().toString(36).substring(2, 9);
};
