import React from 'react';
import { Typography, Box, Chip, Stack } from '@mui/material';
import { extractLanguageCodes } from './excelUtils';

interface LanguageCodeChipsProps {
  columnHeaders: string[];
}

const LanguageCodeChips: React.FC<LanguageCodeChipsProps> = ({ columnHeaders }) => {
  const languageHeaders = extractLanguageCodes(columnHeaders);

  if (languageHeaders.length === 0) {
    return null;
  }

  return (
    <Box sx={{ px: 2, pb: 2 }}>
      <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
        Language Codes:
      </Typography>
      <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
        {languageHeaders.map((header, index) => (
          <Chip
            key={index}
            label={header}
            size="small"
            variant="outlined"
            color="success"
            sx={{ mb: 1 }}
          />
        ))}
      </Stack>
    </Box>
  );
};

export default LanguageCodeChips;
