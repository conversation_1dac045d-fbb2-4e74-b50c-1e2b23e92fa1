import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  Stack,
} from '@mui/material';
import type { SelectChangeEvent } from '@mui/material/Select';
import type { UploadedFile } from '../types';
import { extractLanguageCodes } from './excelUtils';

interface ExcelFileSelectorProps {
  files: UploadedFile[];
  selectedFile: UploadedFile | null;
  onFileSelect: (file: UploadedFile | null) => void;
}

const ExcelFileSelector: React.FC<ExcelFileSelectorProps> = ({
  files,
  selectedFile,
  onFileSelect,
}) => {
  const handleChange = (event: SelectChangeEvent<string>) => {
    const fileId = event.target.value;
    if (fileId === '') {
      onFileSelect(null);
    } else {
      const file = files.find(f => f.id === fileId);
      onFileSelect(file || null);
    }
  };

  const selectedLanguageCodes = selectedFile?.columnHeaders 
    ? extractLanguageCodes(selectedFile.columnHeaders)
    : [];

  return (
    <Box sx={{ mb: 3 }}>
      <FormControl fullWidth size="small">
        <InputLabel id="excel-file-select-label">
          Select Excel File for Language Codes
        </InputLabel>
        <Select
          labelId="excel-file-select-label"
          id="excel-file-select"
          value={selectedFile?.id || ''}
          label="Select Excel File for Language Codes"
          onChange={handleChange}
        >
          <MenuItem value="">
            <em>None (show all language codes)</em>
          </MenuItem>
          {files.map((file) => {
            const languageCount = file.columnHeaders 
              ? extractLanguageCodes(file.columnHeaders).length 
              : 0;
            return (
              <MenuItem key={file.id} value={file.id}>
                {file.name} ({languageCount} language codes)
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>

      {selectedFile && selectedLanguageCodes.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Language codes from "{selectedFile.name}":
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {selectedLanguageCodes.map((code, index) => (
              <Chip
                key={index}
                label={code}
                size="small"
                variant="filled"
                color="primary"
                sx={{ mb: 1 }}
              />
            ))}
          </Stack>
        </Box>
      )}
    </Box>
  );
};

export default ExcelFileSelector;
