import React from 'react';
import { Typography, Box } from '@mui/material';
import { TableChart as ExcelIcon } from '@mui/icons-material';

const ExcelSectionHeader: React.FC = () => {
  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <ExcelIcon sx={{ mr: 1, color: 'success.main' }} />
        <Typography variant="h5" component="h2">
          Excel Documents
        </Typography>
      </Box>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Upload Excel files (.xlsx, .xls) for data analysis and comparison.
      </Typography>
    </>
  );
};

export default ExcelSectionHeader;
