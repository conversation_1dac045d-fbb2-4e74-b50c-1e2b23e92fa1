import { useCallback } from 'react';
import type { UploadedFile } from '../types';
import { readExcelHeaders, generateFileId } from './excelUtils';

interface UseExcelUploadProps {
  onFileUpload: (files: UploadedFile[]) => void;
}

export const useExcelUpload = ({ onFileUpload }: UseExcelUploadProps) => {
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = event.target.files;
    if (!fileList) return;

    const newFiles: UploadedFile[] = [];

    for (const file of Array.from(fileList)) {
      try {
        const columnHeaders = await readExcelHeaders(file);
        newFiles.push({
          id: generateFileId(),
          name: file.name,
          size: file.size,
          type: file.type,
          category: 'excel' as const,
          columnHeaders,
        });
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        // Add file without headers if reading fails
        newFiles.push({
          id: generateFileId(),
          name: file.name,
          size: file.size,
          type: file.type,
          category: 'excel' as const,
          columnHeaders: [],
        });
      }
    }

    onFileUpload(newFiles);
  }, [onFileUpload]);

  return { handleFileUpload };
};
