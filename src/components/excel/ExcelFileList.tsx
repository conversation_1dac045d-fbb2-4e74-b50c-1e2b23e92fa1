import React from 'react';
import { Typography, List } from '@mui/material';
import type { UploadedFile } from '../types';
import ExcelFileItem from './ExcelFileItem';

interface ExcelFileListProps {
  files: UploadedFile[];
  onFileDelete: (fileId: string) => void;
  selectedFileId?: string | null;
}

const ExcelFileList: React.FC<ExcelFileListProps> = ({ files, onFileDelete, selectedFileId }) => {
  return (
    <>
      <Typography variant="h6" gutterBottom>
        Uploaded Excel Files ({files.length})
      </Typography>
      
      {files.length === 0 ? (
        <Typography variant="body2" color="text.secondary">
          No Excel files uploaded yet.
        </Typography>
      ) : (
        <List dense>
          {files.map((file) => (
            <ExcelFileItem
              key={file.id}
              file={file}
              onDelete={onFileDelete}
              isSelected={file.id === selectedFileId}
            />
          ))}
        </List>
      )}
    </>
  );
};

export default ExcelFileList;
