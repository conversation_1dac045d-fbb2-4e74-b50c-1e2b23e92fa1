import React, { createContext, useContext, useState, type ReactNode } from 'react';
import type { UploadedFile } from '../components/types';

interface FileUploadContextType {
  // Excel files and their data
  excelFiles: UploadedFile[];
  setExcelFiles: React.Dispatch<React.SetStateAction<UploadedFile[]>>;
  addExcelFiles: (files: UploadedFile[]) => void;
  removeExcelFile: (fileId: string) => void;

  // PDF files
  pdfFiles: UploadedFile[];
  setPdfFiles: React.Dispatch<React.SetStateAction<UploadedFile[]>>;
  addPdfFiles: (files: UploadedFile[]) => void;
  removePdfFile: (fileId: string) => void;
  updatePdfLanguage: (fileId: string, language: string) => void;

  // Language codes extracted from Excel files
  availableLanguageCodes: string[];

  // Selected Excel file for language code replacement
  selectedExcelFile: UploadedFile | null;
  setSelectedExcelFile: (file: UploadedFile | null) => void;
  selectedFileLanguageCodes: string[];

  // Utility functions
  clearAllFiles: () => void;
  getTotalFileCount: () => number;
}

const FileUploadContext = createContext<FileUploadContextType | undefined>(undefined);

interface FileUploadProviderProps {
  children: ReactNode;
}

export const FileUploadProvider: React.FC<FileUploadProviderProps> = ({ children }) => {
  const [excelFiles, setExcelFiles] = useState<UploadedFile[]>([]);
  const [pdfFiles, setPdfFiles] = useState<UploadedFile[]>([]);
  const [selectedExcelFile, setSelectedExcelFile] = useState<UploadedFile | null>(null);

  // Extract unique language codes from all Excel files
  const availableLanguageCodes = React.useMemo(() => {
    const allLanguageCodes = excelFiles.flatMap(file =>
      file.columnHeaders?.filter(header => header.includes('_')) || []
    );
    return [...new Set(allLanguageCodes)].sort();
  }, [excelFiles]);

  // Extract language codes from selected Excel file only
  const selectedFileLanguageCodes = React.useMemo(() => {
    if (!selectedExcelFile || !selectedExcelFile.columnHeaders) {
      return [];
    }
    return selectedExcelFile.columnHeaders.filter(header => header.includes('_')).sort();
  }, [selectedExcelFile]);

  // Excel file management functions
  const addExcelFiles = (files: UploadedFile[]) => {
    setExcelFiles(prev => [...prev, ...files]);
  };

  const removeExcelFile = (fileId: string) => {
    setExcelFiles(prev => prev.filter(file => file.id !== fileId));
    // Clear selection if the selected file is being removed
    if (selectedExcelFile?.id === fileId) {
      setSelectedExcelFile(null);
    }
  };

  // PDF file management functions
  const addPdfFiles = (files: UploadedFile[]) => {
    setPdfFiles(prev => [...prev, ...files]);
  };

  const removePdfFile = (fileId: string) => {
    setPdfFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const updatePdfLanguage = (fileId: string, language: string) => {
    setPdfFiles(prev => prev.map(file =>
      file.id === fileId
        ? { ...file, selectedLanguage: language }
        : file
    ));
  };

  // Utility functions
  const clearAllFiles = () => {
    setExcelFiles([]);
    setPdfFiles([]);
  };

  const getTotalFileCount = () => {
    return excelFiles.length + pdfFiles.length;
  };

  const contextValue: FileUploadContextType = {
    excelFiles,
    setExcelFiles,
    addExcelFiles,
    removeExcelFile,
    pdfFiles,
    setPdfFiles,
    addPdfFiles,
    removePdfFile,
    updatePdfLanguage,
    availableLanguageCodes,
    selectedExcelFile,
    setSelectedExcelFile,
    selectedFileLanguageCodes,
    clearAllFiles,
    getTotalFileCount,
  };

  return (
    <FileUploadContext.Provider value={contextValue}>
      {children}
    </FileUploadContext.Provider>
  );
};

// Custom hook to use the FileUpload context
export const useFileUpload = (): FileUploadContextType => {
  const context = useContext(FileUploadContext);
  if (context === undefined) {
    throw new Error('useFileUpload must be used within a FileUploadProvider');
  }
  return context;
};

export default FileUploadContext;
