import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  Chip,
  Stack,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Home as HomeIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { ExcelUploadSection, PdfUploadSection } from '../components';
import { useFileUpload } from '../context/FileUploadContext';

const StyledContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
 }));

const FileUploads: React.FC = () => {
  const navigate = useNavigate();
  const {
    excelFiles,
    pdfFiles,
    addExcelFiles,
    addPdfFiles,
    removeExcelFile,
    removePdfFile,
    availableLanguageCodes,
    selectedExcelFile,
    selectedFileLanguageCodes,
  } = useFileUpload();

  const handleExcelUpload = (newFiles: any[]) => {
    addExcelFiles(newFiles);
  };

  const handlePdfUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files).map((file) => ({
        id: Math.random().toString(36).substring(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        category: 'pdf' as const,
        originalFile: file, // Store the original File object for preview
      }));
      addPdfFiles(newFiles);
    }
  };

  const handleDeleteExcelFile = (fileId: string) => {
    removeExcelFile(fileId);
  };

  const handleDeletePdfFile = (fileId: string) => {
    removePdfFile(fileId);
  };



  const handleNavigateHome = () => {
    navigate('/');
  };

  return (
    <StyledContainer sx={{ width: "100vw" }}>
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h3" component="h1">
          File Uploads
        </Typography>
        <Button
          variant="outlined"
          startIcon={<HomeIcon />}
          onClick={handleNavigateHome}
        >
          Back to Home
        </Button>
      </Box>

      <Box sx={{ display: 'flex', gap: 4, flexDirection: { xs: 'column', md: 'row' } }}>
        <ExcelUploadSection
          files={excelFiles}
          onFileUpload={handleExcelUpload}
          onFileDelete={handleDeleteExcelFile}
        />

        <PdfUploadSection
          files={pdfFiles}
          onFileUpload={handlePdfUpload}
          onFileDelete={handleDeletePdfFile}
        />
      </Box>

      {/* Language Codes Summary */}
      {(selectedFileLanguageCodes.length > 0 || availableLanguageCodes.length > 0) && (
        <Paper sx={{ mt: 4, p: 3 }}>
          {selectedExcelFile ? (
            <>
              <Typography variant="h6" gutterBottom>
                Language Codes from Selected File ({selectedFileLanguageCodes.length})
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Language codes from "{selectedExcelFile.name}":
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {selectedFileLanguageCodes.map((code, index) => (
                  <Chip
                    key={index}
                    label={code}
                    size="medium"
                    variant="filled"
                    color="primary"
                    sx={{ mb: 1 }}
                  />
                ))}
              </Stack>
            </>
          ) : (
            <>
              <Typography variant="h6" gutterBottom>
                All Available Language Codes ({availableLanguageCodes.length})
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Language codes extracted from all uploaded Excel files:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {availableLanguageCodes.map((code, index) => (
                  <Chip
                    key={index}
                    label={code}
                    size="medium"
                    variant="filled"
                    color="secondary"
                    sx={{ mb: 1 }}
                  />
                ))}
              </Stack>
            </>
          )}
        </Paper>
      )}

      {/* PDF Language Selections Summary */}
      {pdfFiles.some(file => file.selectedLanguage) && (
        <Paper sx={{ mt: 2, p: 3 }}>
          <Typography variant="h6" gutterBottom>
            PDF Language Selections
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Selected languages for PDF files:
          </Typography>
          <Stack spacing={2}>
            {pdfFiles
              .filter(file => file.selectedLanguage)
              .map((file) => (
                <Box key={file.id} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="body2" sx={{ minWidth: 200, fontWeight: 500 }}>
                    {file.name}:
                  </Typography>
                  <Chip
                    label={file.selectedLanguage}
                    size="small"
                    variant="filled"
                    color="error"
                  />
                </Box>
              ))}
          </Stack>
        </Paper>
      )}

      {/* Compare Button */}
      {(excelFiles.length > 0 || pdfFiles.length > 0) && (
        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            disabled={excelFiles.length === 0 || pdfFiles.length === 0}
          >
            Compare Documents ({excelFiles.length} Excel + {pdfFiles.length} PDF)
          </Button>
          {(excelFiles.length === 0 || pdfFiles.length === 0) && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Upload at least one Excel file and one PDF file to enable comparison
            </Typography>
          )}
        </Box>
      )}
    </StyledContainer>
  );
};

export default FileUploads;
