import React from 'react';
import { Container, Typography, Box, Button, Chip, Stack } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Home as HomeIcon, Upload as UploadIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useFileUpload } from '../context/FileUploadContext';

const StyledContainer = styled(Container)(() => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '100vh',
  textAlign: 'center',
}));

const StyledButton = styled(Button)(({ theme }) => ({
  margin: theme.spacing(2),
  padding: theme.spacing(1.5, 4),
}));

const Home: React.FC = () => {
  const navigate = useNavigate();
  const {
    getTotalFileCount,
    availableLanguageCodes,
    selectedFileLanguageCodes,
    selectedExcelFile,
    excelFiles,
    pdfFiles
  } = useFileUpload();

  const handleNavigateToUpload = () => {
    navigate('/file-uploads');
  };

  return (
    <StyledContainer maxWidth="md">
      <Box sx={{ mb: 4 }}>
        <HomeIcon sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
        <Typography variant="h2" component="h1" gutterBottom>
          Narratives Compare
        </Typography>
        <Typography variant="h5" component="h2" color="text.secondary" gutterBottom>
          Welcome to the Document Comparison Tool
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mt: 2, mb: 4 }}>
          Upload and compare documents to analyze narratives and differences.
        </Typography>
      </Box>
      
      <Box>
        <StyledButton
          variant="contained"
          size="large"
          startIcon={<UploadIcon />}
          onClick={handleNavigateToUpload}
        >
          Start Uploading Files
        </StyledButton>
      </Box>

      {/* Current Files Summary */}
      {getTotalFileCount() > 0 && (
        <Box sx={{ mt: 4, p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom>
            Current Session Summary
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            📊 Excel Files: {excelFiles.length} | 📄 PDF Files: {pdfFiles.length}
          </Typography>

          {selectedExcelFile ? (
            <>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Selected File: {selectedExcelFile.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Language Codes:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {selectedFileLanguageCodes.slice(0, 10).map((code, index) => (
                  <Chip
                    key={index}
                    label={code}
                    size="small"
                    variant="filled"
                    color="primary"
                  />
                ))}
                {selectedFileLanguageCodes.length > 10 && (
                  <Chip
                    label={`+${selectedFileLanguageCodes.length - 10} more`}
                    size="small"
                    variant="outlined"
                    color="secondary"
                  />
                )}
              </Stack>
            </>
          ) : availableLanguageCodes.length > 0 ? (
            <>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                All Available Language Codes:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {availableLanguageCodes.slice(0, 10).map((code, index) => (
                  <Chip
                    key={index}
                    label={code}
                    size="small"
                    variant="outlined"
                    color="secondary"
                  />
                ))}
                {availableLanguageCodes.length > 10 && (
                  <Chip
                    label={`+${availableLanguageCodes.length - 10} more`}
                    size="small"
                    variant="outlined"
                    color="secondary"
                  />
                )}
              </Stack>
            </>
          ) : null}
        </Box>
      )}
    </StyledContainer>
  );
};

export default Home;
