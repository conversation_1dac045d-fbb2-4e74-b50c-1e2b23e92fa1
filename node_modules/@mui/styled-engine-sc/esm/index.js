/**
 * @mui/styled-engine-sc v7.1.1
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import scStyled from 'styled-components';
export default function styled(tag, options) {
  let stylesFactory;
  if (options) {
    stylesFactory = scStyled(tag).withConfig({
      displayName: options.label,
      shouldForwardProp: options.shouldForwardProp
    });
  } else {
    stylesFactory = scStyled(tag);
  }
  if (process.env.NODE_ENV !== 'production') {
    const fn = (...styles) => {
      const component = typeof tag === 'string' ? `"${tag}"` : 'component';
      if (styles.length === 0) {
        console.error([`MUI: Seems like you called \`styled(${component})()\` without a \`style\` argument.`, 'You must provide a `styles` argument: `styled("div")(styleYouForgotToPass)`.'].join('\n'));
      } else if (styles.some(style => style === undefined)) {
        console.error(`MUI: the styled(${component})(...args) API requires all its args to be defined.`);
      }
      return stylesFactory(...styles);
    };
    fn.withConfig = stylesFactory.withConfig;
    return fn;
  }
  return stylesFactory;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export function internal_mutateStyles(tag, processor) {
  // Styled-components attaches an instance to `componentStyle`.
  // https://github.com/styled-components/styled-components/blob/da8151762dcf72735ffba358173d4c097f6d5888/packages/styled-components/src/models/StyledComponent.ts#L257
  //
  // The instance contains `rules` (the styles)
  // https://github.com/styled-components/styled-components/blob/da8151762dcf72735ffba358173d4c097f6d5888/packages/styled-components/src/models/ComponentStyle.ts#L23
  if (tag.componentStyle) {
    tag.componentStyle.rules = processor(tag.componentStyle.rules);
  }
}

// Not needed anymore, but fixes https://github.com/mui/material-ui/issues/44112
// TODO: Remove it in v7
// eslint-disable-next-line @typescript-eslint/naming-convention
export function internal_processStyles(tag, processor) {
  return internal_mutateStyles(tag, processor);
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export function internal_serializeStyles(styles) {
  return styles;
}
export { ThemeContext, keyframes, css } from 'styled-components';
export { default as StyledEngineProvider } from "./StyledEngineProvider/index.js";
export { default as GlobalStyles } from "./GlobalStyles/index.js";