{"name": "@mui/styled-engine-sc", "version": "7.1.1", "author": "MUI Team", "description": "styled() API wrapper package for styled-components.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "styled-components"], "repository": {"type": "git", "url": "git+https://github.com/mui/material-ui.git", "directory": "packages/mui-styled-engine-sc"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/material-ui/integrations/styled-components/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.27.1", "@types/hoist-non-react-statics": "^3.3.6", "csstype": "^3.1.3", "hoist-non-react-statics": "^3.3.2", "prop-types": "^15.8.1"}, "peerDependencies": {"styled-components": "^6.0.0"}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=14.0.0"}, "private": false, "module": "./esm/index.js", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./*/index.d.ts", "default": "./*/index.js"}, "import": {"types": "./esm/*/index.d.ts", "default": "./esm/*/index.js"}}, "./esm": null, "./modern": null}, "types": "./index.d.ts"}