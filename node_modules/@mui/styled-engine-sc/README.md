# @mui/styled-engine-sc

This package is a wrapper around the `styled-components` package implementing the interface of `@mui/styled-engine`.
It's designed for developers who would like to use `styled-components` as the main styled engine instead of `@emotion/styled`.

## Documentation

<!-- #host-reference -->

Visit [https://mui.com/material-ui/integrations/styled-components/](https://mui.com/material-ui/integrations/styled-components/) to view the full documentation.
